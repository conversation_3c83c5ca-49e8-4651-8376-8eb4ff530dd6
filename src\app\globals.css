@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&family=Almarai:wght@300;400;700;800&display=swap');
@import "tailwindcss";

@theme {
  /* Colors - Light Mode */
  --color-background: 220 20% 97%;
  --color-foreground: 210 15% 10%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 210 15% 10%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 210 15% 10%;
  
  /* Premium Blue Primary */
  --color-primary: 217 91% 60%;
  --color-primary-foreground: 0 0% 100%;
  --color-primary-light: 217 91% 75%;
  --color-primary-dark: 217 91% 45%;
  
  /* Premium Secondary */
  --color-secondary: 210 20% 95%;
  --color-secondary-foreground: 210 15% 15%;
  
  /* Muted colors */
  --color-muted: 210 15% 93%;
  --color-muted-foreground: 210 10% 45%;
  
  /* Accent colors */
  --color-accent: 217 91% 60%;
  --color-accent-foreground: 0 0% 100%;
  
  /* Status colors */
  --color-success: 142 71% 45%;
  --color-success-foreground: 0 0% 100%;
  --color-warning: 38 92% 50%;
  --color-warning-foreground: 0 0% 100%;
  --color-destructive: 0 84% 60%;
  --color-destructive-foreground: 0 0% 100%;
  
  /* Borders and inputs */
  --color-border: 210 20% 85%;
  --color-input: 210 20% 90%;
  --color-ring: 217 91% 60%;
  
  /* Sidebar specific */
  --color-sidebar-background: 0 0% 100%;
  --color-sidebar-foreground: 210 15% 10%;
  --color-sidebar-primary: 217 91% 60%;
  --color-sidebar-primary-foreground: 0 0% 100%;
  --color-sidebar-accent: 210 20% 95%;
  --color-sidebar-accent-foreground: 210 15% 15%;
  --color-sidebar-border: 210 20% 85%;
  --color-sidebar-ring: 217 91% 60%;

  /* Dark Mode Colors */
  @media (prefers-color-scheme: dark) {
    --color-background: 210 20% 3%;
    --color-foreground: 210 15% 95%;
    --color-card: 210 15% 5%;
    --color-card-foreground: 210 15% 95%;
    --color-popover: 210 15% 5%;
    --color-popover-foreground: 210 15% 95%;
    
    /* Primary stays vibrant in dark */
    --color-primary: 217 91% 65%;
    --color-primary-foreground: 210 20% 3%;
    --color-primary-light: 217 91% 75%;
    --color-primary-dark: 217 91% 55%;
    
    --color-secondary: 210 15% 8%;
    --color-secondary-foreground: 210 15% 85%;
    
    --color-muted: 210 15% 7%;
    --color-muted-foreground: 210 10% 55%;
    
    --color-accent: 217 91% 65%;
    --color-accent-foreground: 210 20% 3%;
    
    --color-success: 142 71% 55%;
    --color-warning: 38 92% 60%;
    --color-destructive: 0 84% 65%;
    
    --color-border: 210 15% 15%;
    --color-input: 210 15% 10%;
    --color-ring: 217 91% 65%;
    
    --color-sidebar-background: 210 15% 4%;
    --color-sidebar-foreground: 210 15% 90%;
    --color-sidebar-primary: 217 91% 65%;
    --color-sidebar-primary-foreground: 210 20% 3%;
    --color-sidebar-accent: 210 15% 8%;
    --color-sidebar-accent-foreground: 210 15% 85%;
    --color-sidebar-border: 210 15% 12%;
    --color-sidebar-ring: 217 91% 65%;
  }

  /* Radius */
  --radius: 0.75rem;

  /* Font families */
  --font-family-arabic: 'IBM Plex Sans Arabic', sans-serif;
  --font-family-almarai: 'Almarai', sans-serif;

  /* Premium Gradients */
  --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(217 91% 45%) 100%);
  --gradient-secondary: linear-gradient(135deg, hsl(210 20% 95%) 0%, hsl(210 15% 85%) 100%);
  --gradient-dark: linear-gradient(135deg, hsl(210 15% 10%) 0%, hsl(210 20% 5%) 100%);
  --gradient-glass: linear-gradient(135deg, hsla(217 91% 60% / 0.1) 0%, hsla(217 91% 45% / 0.05) 100%);
  
  /* Premium Shadows */
  --shadow-soft: 0 2px 10px -3px hsl(217 91% 60% / 0.1);
  --shadow-medium: 0 8px 30px -12px hsl(217 91% 60% / 0.25);
  --shadow-strong: 0 20px 40px -12px hsl(217 91% 60% / 0.4);
  --shadow-glow: 0 0 0 1px hsl(217 91% 60% / 0.1), 0 0 20px hsl(217 91% 60% / 0.3);

  /* Dark mode gradients and shadows */
  @media (prefers-color-scheme: dark) {
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 65%) 0%, hsl(217 91% 50%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(210 15% 8%) 0%, hsl(210 15% 5%) 100%);
    --gradient-dark: linear-gradient(135deg, hsl(210 20% 3%) 0%, hsl(210 15% 1%) 100%);
    --gradient-glass: linear-gradient(135deg, hsla(217 91% 65% / 0.15) 0%, hsla(217 91% 50% / 0.08) 100%);
    
    --shadow-soft: 0 2px 10px -3px hsl(217 91% 65% / 0.2);
    --shadow-medium: 0 8px 30px -12px hsl(217 91% 65% / 0.3);
    --shadow-strong: 0 20px 40px -12px hsl(217 91% 65% / 0.5);
    --shadow-glow: 0 0 0 1px hsl(217 91% 65% / 0.2), 0 0 30px hsl(217 91% 65% / 0.4);
  }

  /* Animations */
  --animate-fade-in-up: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  --animate-scale-in: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  --animate-slide-in-right: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* RTL Support */
html {
  direction: rtl;
}

body {
  background-color: hsl(var(--color-background));
  color: hsl(var(--color-foreground));
  font-family: var(--font-family-arabic);
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

/* Border color for all elements */
* {
  border-color: hsl(var(--color-border));
}

/* Premium scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: hsl(var(--color-muted) / 0.3);
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--color-primary) / 0.4);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--color-primary) / 0.6);
}

/* Component classes */
.glass-effect {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid hsl(var(--color-border) / 0.2);
}

.premium-card {
  background-color: hsl(var(--color-card));
  border: 1px solid hsl(var(--color-border) / 0.5);
  border-radius: calc(var(--radius) + 4px);
  box-shadow: var(--shadow-soft);
  background: linear-gradient(135deg, hsl(var(--color-card)) 0%, hsl(var(--color-card) / 0.95) 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.btn-premium {
  position: relative;
  overflow: hidden;
  background: var(--gradient-primary);
  box-shadow: var(--shadow-soft);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-premium:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.input-premium {
  background-color: hsl(var(--color-card));
  border: 1px solid hsl(var(--color-border) / 0.5);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-premium:focus {
  border-color: hsl(var(--color-primary) / 0.5);
  box-shadow: 0 0 0 2px hsl(var(--color-primary) / 0.2), var(--shadow-glow);
  outline: none;
}

.heading-arabic {
  font-family: var(--font-family-almarai);
  font-weight: 700;
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px hsl(var(--color-foreground) / 0.1);
}

.text-arabic {
  font-family: var(--font-family-arabic);
  line-height: 1.625;
}

/* Animation classes */
.fade-in-up {
  animation: var(--animate-fade-in-up);
}

.scale-in {
  animation: var(--animate-scale-in);
}

.slide-in-right {
  animation: var(--animate-slide-in-right);
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  .premium-card {
    border-radius: var(--radius);
    border: none;
    box-shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
    background: linear-gradient(135deg, hsl(var(--color-card)) 0%, hsl(var(--color-card) / 0.98) 100%);
  }
  
  .glass-effect {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .mobile-app-layout {
    min-height: 100vh;
    background: linear-gradient(to bottom right, hsl(var(--color-background)), hsl(var(--color-background)), hsl(var(--color-muted) / 0.1));
  }
  
  .sidebar-mobile {
    width: 100%;
    max-width: 20rem;
    box-shadow: 0 25px 50px -12px hsl(0 0% 0% / 0.25);
  }
  
  .mobile-text-lg {
    font-size: 1.125rem;
    line-height: 1.375;
  }
  
  .mobile-text-base {
    font-size: 1rem;
    line-height: 1.375;
  }
  
  .mobile-card-spacing {
    padding: 1rem;
    gap: 1rem;
  }
  
  .mobile-btn {
    height: 3rem;
    padding: 0 1.5rem;
    border-radius: calc(var(--radius) + 4px);
    font-weight: 500;
  }
  
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
    padding-top: env(safe-area-inset-top);
  }
  
  .sidebar-mobile {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 50;
    width: 20rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }
  
  .sidebar-mobile[data-state="collapsed"] {
    transform: translateX(-100%);
  }
  
  .sidebar-mobile[data-state="expanded"] {
    transform: translateX(0);
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .premium-card {
    background: linear-gradient(135deg, hsl(var(--color-card)) 0%, hsl(var(--color-card) / 0.98) 100%);
    border-color: hsl(var(--color-border) / 0.3);
  }
  
  .glass-effect {
    background: linear-gradient(135deg, hsla(217 91% 65% / 0.08) 0%, hsla(217 91% 50% / 0.04) 100%);
    border-color: hsl(var(--color-border) / 0.1);
  }
}

/* Touch improvements for mobile */
@media (hover: none) and (pointer: coarse) {
  .hover-scale {
    transform: none;
  }
  
  .premium-card:hover {
    transform: none;
  }
  
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}